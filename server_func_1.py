# server_func_1.py - 服务端中间层函数文件
# 版本: 1.0 - 支持动态重载和依赖管理
import importlib
import sys
import time

# 模块版本标识
MODULE_VERSION = "1.0"
MODULE_NAME = "server_func_1"

# 动态导入底层模块 - 支持热重载
def _reload_dependencies():
    """
    重载依赖模块 - 实现热更新的关键
    """
    if 'server_func_2' in sys.modules:
        importlib.reload(sys.modules['server_func_2'])

    # 重新导入函数
    global basic_math_operation, format_result, advanced_operation
    from server_func_2 import basic_math_operation, format_result, advanced_operation

# 初始导入
_reload_dependencies()

def get_module_info():
    """
    获取模块信息 - 包含依赖信息
    """
    _reload_dependencies()  # 确保获取最新的依赖信息
    from server_func_2 import get_module_info as get_func2_info

    return {
        "name": MODULE_NAME,
        "version": MODULE_VERSION,
        "functions": ["advanced_calculator", "batch_calculate", "smart_calculator"],
        "dependencies": [get_func2_info()],
        "last_modified": time.time()
    }

def advanced_calculator(num1, num2, operation):
    """
    高级计算器函数 - 自动重载最新功能
    """
    _reload_dependencies()  # 每次调用前重载，确保使用最新代码

    print(f"[{MODULE_NAME} v{MODULE_VERSION}] 正在执行 {operation} 运算: {num1} 和 {num2}")

    # 调用底层函数进行计算
    result = basic_math_operation(num1, num2, operation)

    # 格式化结果
    formatted_result = format_result(result, operation)

    return formatted_result

def batch_calculate(numbers_list, operation):
    """
    批量计算函数 - 支持热更新
    """
    _reload_dependencies()

    if len(numbers_list) < 2:
        return "错误：至少需要两个数字进行运算"

    result = numbers_list[0]
    for i in range(1, len(numbers_list)):
        result = basic_math_operation(result, numbers_list[i], operation)
        if isinstance(result, str) and "错误" in result:
            return result

    return format_result(result, f"批量{operation}")

def smart_calculator(num1, num2=None, operation='auto'):
    """
    智能计算器 - 新增功能，演示热更新
    """
    _reload_dependencies()

    if operation == 'auto':
        # 智能选择运算类型
        if num2 is None:
            return advanced_operation(num1, 0, 'factorial')
        elif num1 > num2:
            return basic_math_operation(num1, num2, 'subtract')
        else:
            return basic_math_operation(num1, num2, 'add')
    else:
        if num2 is None:
            return advanced_operation(num1, 0, operation)
        return basic_math_operation(num1, num2, operation)

def get_calculation_info():
    """
    获取计算器信息 - 动态获取最新功能
    """
    _reload_dependencies()

    return {
        "version": MODULE_VERSION,
        "supported_operations": ["add", "subtract", "multiply", "divide", "power", "factorial", "gcd"],
        "features": ["单次计算", "批量计算", "智能计算", "热更新支持"],
        "new_features": ["智能运算选择", "高级数学运算"]
    }
