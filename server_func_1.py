# server_func_1.py - 服务端中间层函数文件
# 从server_func_2导入基础函数

from server_func_2 import basic_math_operation, format_result

def advanced_calculator(num1, num2, operation):
    """
    高级计算器函数
    调用底层的basic_math_operation函数
    """
    print(f"正在执行 {operation} 运算: {num1} 和 {num2}")
    
    # 调用底层函数进行计算
    result = basic_math_operation(num1, num2, operation)
    
    # 格式化结果
    formatted_result = format_result(result, operation)
    
    return formatted_result

def batch_calculate(numbers_list, operation):
    """
    批量计算函数
    对数字列表进行批量运算
    """
    if len(numbers_list) < 2:
        return "错误：至少需要两个数字进行运算"
    
    result = numbers_list[0]
    for i in range(1, len(numbers_list)):
        result = basic_math_operation(result, numbers_list[i], operation)
        if isinstance(result, str) and "错误" in result:
            return result
    
    return format_result(result, f"批量{operation}")

def get_calculation_info():
    """
    获取计算器信息
    """
    return {
        "version": "1.0",
        "supported_operations": ["add", "subtract", "multiply", "divide"],
        "features": ["单次计算", "批量计算", "结果格式化"]
    }
