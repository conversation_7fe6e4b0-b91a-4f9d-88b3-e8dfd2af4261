# Python 热更新系统使用说明

## 系统概述

这是一个基于Socket通信的Python热更新系统，允许客户端从服务端动态获取并执行代码，实现无需重启的功能更新。

## 系统架构

```
client.py (客户端)
    ↓ Socket请求
server.py (服务端主控制器)
    ↓ 导入函数
server_func_1.py (中间层函数)
    ↓ 导入函数  
server_func_2.py (底层函数)
```

## 详细流程说明

### 1. 系统启动流程

#### 服务端启动：
1. `server.py` 启动，创建Socket服务器监听8888端口
2. `server.py` 导入 `server_func_1.py` 中的函数
3. `server_func_1.py` 导入 `server_func_2.py` 中的基础函数
4. 服务器进入监听状态，等待客户端连接

#### 客户端启动：
1. `client.py` 启动，创建Socket客户端
2. 自动运行演示程序
3. 进入交互模式等待用户输入

### 2. 热更新执行流程

```mermaid
sequenceDiagram
    participant C as Client (client.py)
    participant S as Server (server.py)
    participant F1 as server_func_1.py
    participant F2 as server_func_2.py
    
    C->>S: 发送JSON请求 {"action": "calculate", "params": {...}}
    S->>F1: 调用相关函数获取逻辑
    F1->>F2: 调用底层函数
    F2-->>F1: 返回计算结果
    F1-->>S: 返回处理结果
    S->>S: 生成可执行代码字符串
    S-->>C: 返回代码字符串
    C->>C: exec(代码字符串) 执行
    C->>C: 显示执行结果
```

### 3. 核心机制详解

#### 3.1 请求格式
客户端发送的JSON请求格式：
```json
{
    "action": "操作类型",
    "params": {
        "参数1": "值1",
        "参数2": "值2"
    }
}
```

#### 3.2 代码生成机制
服务端的 `generate_executable_code()` 函数根据请求生成Python代码字符串：

```python
def generate_executable_code(self, request_data):
    action = request_data.get('action', '')
    params = request_data.get('params', {})
    
    if action == 'calculate':
        # 生成计算相关的可执行代码
        code = f"""
def execute_calculation():
    # 在这里嵌入服务端的函数逻辑
    result = basic_math_operation({num1}, {num2}, '{operation}')
    return result
result = execute_calculation()
"""
        return code
```

#### 3.3 客户端执行机制
客户端使用 `exec()` 执行从服务端获取的代码：

```python
def execute_hot_update(self, action, params=None):
    response = self.send_request(action, params)
    if response and response.get('status') == 'success':
        executable_code = response.get('code', '')
        exec(executable_code)  # 核心：执行服务端代码
```

## 如何添加自定义函数

### 方法一：在现有文件中添加函数

#### 1. 在 `server_func_2.py` 中添加底层函数

```python
# 在 server_func_2.py 末尾添加
def your_custom_function(param1, param2):
    """
    你的自定义函数
    """
    # 实现你的逻辑
    result = f"处理 {param1} 和 {param2} 的结果"
    return result

def another_helper_function():
    """
    辅助函数
    """
    return "辅助数据"
```

#### 2. 在 `server_func_1.py` 中导入并封装

```python
# 在文件顶部添加导入
from server_func_2 import basic_math_operation, format_result, your_custom_function, another_helper_function

# 添加中间层函数
def enhanced_custom_operation(param1, param2, extra_param=None):
    """
    增强的自定义操作
    """
    # 调用底层函数
    base_result = your_custom_function(param1, param2)
    helper_data = another_helper_function()
    
    # 添加额外处理逻辑
    if extra_param:
        base_result += f" (额外参数: {extra_param})"
    
    return f"{base_result} | {helper_data}"
```

#### 3. 在 `server.py` 中添加代码生成逻辑

```python
# 在 generate_executable_code 方法中添加新的 elif 分支
elif action == 'custom_operation':
    param1 = params.get('param1', '')
    param2 = params.get('param2', '')
    extra_param = params.get('extra_param', None)
    
    code = f"""
# 热更新代码 - 自定义操作
def execute_custom_operation():
    # 复制服务端函数逻辑到客户端
    def your_custom_function(p1, p2):
        result = f"处理 {{p1}} 和 {{p2}} 的结果"
        return result
    
    def another_helper_function():
        return "辅助数据"
    
    # 执行自定义操作
    base_result = your_custom_function('{param1}', '{param2}')
    helper_data = another_helper_function()
    
    extra_param = {repr(extra_param)}
    if extra_param:
        base_result += f" (额外参数: {{extra_param}})"
    
    final_result = f"{{base_result}} | {{helper_data}}"
    print(f"自定义操作结果: {{final_result}}")
    return final_result

# 执行函数
result = execute_custom_operation()
"""
    return code
```

#### 4. 在 `client.py` 中添加调用方法

```python
# 在 HotUpdateClient 类中添加方法
def demo_custom_operation(self):
    """
    演示自定义操作
    """
    print("【演示】自定义操作")
    self.execute_hot_update('custom_operation', {
        'param1': '数据A',
        'param2': '数据B',
        'extra_param': '额外信息'
    })

# 在 interactive_mode 方法中添加命令
elif command == 'custom':
    param1 = input("请输入参数1: ")
    param2 = input("请输入参数2: ")
    extra_param = input("请输入额外参数 (可选): ").strip()
    
    params = {'param1': param1, 'param2': param2}
    if extra_param:
        params['extra_param'] = extra_param
    
    self.execute_hot_update('custom_operation', params)
```

### 方法二：创建新的函数模块

#### 1. 创建新的函数文件 `server_func_3.py`

```python
# server_func_3.py - 你的自定义函数模块

def process_data(data_list):
    """
    数据处理函数
    """
    processed = [item.upper() if isinstance(item, str) else item * 2 for item in data_list]
    return processed

def validate_input(input_data):
    """
    输入验证函数
    """
    if not input_data:
        return False, "输入不能为空"
    if len(str(input_data)) < 3:
        return False, "输入长度不足"
    return True, "验证通过"
```

#### 2. 在 `server_func_1.py` 中导入新模块

```python
# 添加导入
from server_func_3 import process_data, validate_input

# 添加封装函数
def comprehensive_data_processing(raw_data):
    """
    综合数据处理
    """
    # 验证输入
    is_valid, message = validate_input(raw_data)
    if not is_valid:
        return f"验证失败: {message}"
    
    # 处理数据
    if isinstance(raw_data, list):
        processed = process_data(raw_data)
        return f"处理完成: {processed}"
    else:
        return f"单项处理: {raw_data.upper() if isinstance(raw_data, str) else raw_data * 2}"
```

## 测试你的自定义函数

### 1. 启动测试

```bash
# 终端1：启动服务端
python server.py

# 终端2：启动客户端
python client.py
```

### 2. 测试流程

1. 客户端启动后会自动运行演示
2. 进入交互模式后，输入你添加的命令（如 `custom`）
3. 观察客户端执行从服务端获取的代码
4. 检查结果是否符合预期

### 3. 调试技巧

- 在服务端的 `generate_executable_code` 中添加 `print()` 语句查看生成的代码
- 在客户端的 `execute_hot_update` 中打印接收到的代码
- 使用 `try-except` 包装 `exec()` 调用以捕获执行错误

## 注意事项

1. **安全性**：`exec()` 执行任意代码存在安全风险，生产环境需要添加代码验证
2. **错误处理**：确保服务端生成的代码语法正确
3. **参数传递**：注意字符串参数的引号处理
4. **复杂对象**：复杂数据结构需要特殊处理（如使用 `repr()` 或 `json.dumps()`）
5. **函数依赖**：确保生成的代码包含所有必要的函数定义

## 扩展建议

1. **版本控制**：为不同版本的函数添加版本标识
2. **缓存机制**：缓存常用的代码生成结果
3. **日志记录**：记录所有热更新操作
4. **权限控制**：添加客户端身份验证
5. **代码压缩**：对生成的代码进行压缩传输
