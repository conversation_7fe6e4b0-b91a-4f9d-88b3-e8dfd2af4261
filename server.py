# server.py - 服务端主文件
# 从server_func_1导入函数

import socket
import json
from server_func_1 import advanced_calculator, batch_calculate, get_calculation_info

class HotUpdateServer:
    def __init__(self, host='localhost', port=8888):
        self.host = host
        self.port = port
        self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    
    def generate_executable_code(self, request_data):
        """
        根据请求生成可执行的代码字符串
        这是热更新的核心：返回客户端可以exec()的代码
        """
        action = request_data.get('action', '')
        params = request_data.get('params', {})
        
        if action == 'calculate':
            num1 = params.get('num1', 0)
            num2 = params.get('num2', 0)
            operation = params.get('operation', 'add')
            
            # 生成客户端可执行的代码
            code = f"""
# 热更新代码 - 单次计算
def execute_calculation():
    # 模拟从服务端导入的函数逻辑
    def basic_math_operation(a, b, op):
        if op == 'add':
            return a + b
        elif op == 'subtract':
            return a - b
        elif op == 'multiply':
            return a * b
        elif op == 'divide':
            return a / b if b != 0 else "除数不能为零"
        return "不支持的运算"
    
    def format_result(result, op_type):
        import time
        timestamp = time.time()
        return f"[{{timestamp}}] {{op_type}}运算结果: {{result}}"
    
    # 执行计算
    result = basic_math_operation({num1}, {num2}, '{operation}')
    formatted = format_result(result, '{operation}')
    print(f"服务端计算结果: {{formatted}}")
    return formatted

# 执行函数并返回结果
result = execute_calculation()
"""
            return code
            
        elif action == 'batch_calculate':
            numbers = params.get('numbers', [])
            operation = params.get('operation', 'add')
            
            code = f"""
# 热更新代码 - 批量计算
def execute_batch_calculation():
    def basic_math_operation(a, b, op):
        if op == 'add':
            return a + b
        elif op == 'subtract':
            return a - b
        elif op == 'multiply':
            return a * b
        elif op == 'divide':
            return a / b if b != 0 else "除数不能为零"
        return "不支持的运算"
    
    numbers_list = {numbers}
    operation = '{operation}'
    
    if len(numbers_list) < 2:
        result = "错误：至少需要两个数字"
    else:
        result = numbers_list[0]
        for i in range(1, len(numbers_list)):
            result = basic_math_operation(result, numbers_list[i], operation)
            if isinstance(result, str) and "错误" in str(result):
                break
    
    print(f"批量计算结果: {{result}}")
    return result

# 执行批量计算
result = execute_batch_calculation()
"""
            return code
            
        elif action == 'info':
            code = """
# 热更新代码 - 获取信息
def get_server_info():
    info = {
        "version": "1.0",
        "supported_operations": ["add", "subtract", "multiply", "divide"],
        "features": ["单次计算", "批量计算", "热更新"]
    }
    print(f"服务器信息: {info}")
    return info

# 获取并显示信息
result = get_server_info()
"""
            return code
        
        return "print('未知的操作类型')"
    
    def handle_client(self, client_socket):
        """
        处理客户端请求
        """
        try:
            # 接收请求数据
            data = client_socket.recv(1024).decode('utf-8')
            request = json.loads(data)
            
            print(f"收到请求: {request}")
            
            # 生成可执行代码
            executable_code = self.generate_executable_code(request)
            
            # 发送代码给客户端
            response = {
                'status': 'success',
                'code': executable_code
            }
            
            client_socket.send(json.dumps(response, ensure_ascii=False).encode('utf-8'))
            
        except Exception as e:
            error_response = {
                'status': 'error',
                'message': str(e)
            }
            client_socket.send(json.dumps(error_response, ensure_ascii=False).encode('utf-8'))
        
        finally:
            client_socket.close()
    
    def start(self):
        """
        启动服务器
        """
        self.socket.bind((self.host, self.port))
        self.socket.listen(5)
        print(f"热更新服务器启动在 {self.host}:{self.port}")
        
        try:
            while True:
                client_socket, address = self.socket.accept()
                print(f"客户端连接: {address}")
                self.handle_client(client_socket)
        except KeyboardInterrupt:
            print("\n服务器关闭")
        finally:
            self.socket.close()

if __name__ == "__main__":
    server = HotUpdateServer()
    server.start()
