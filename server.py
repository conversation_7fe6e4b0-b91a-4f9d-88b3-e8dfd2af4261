# server.py - 服务端主文件
# 支持模块热重载的服务端

import socket
import json
import importlib
import sys
import time
import threading

# 动态导入模块 - 支持热重载
def reload_all_modules():
    """
    重载所有业务模块
    """
    modules_to_reload = ['server_func_2', 'server_func_1']

    for module_name in modules_to_reload:
        if module_name in sys.modules:
            importlib.reload(sys.modules[module_name])
            print(f"[热更新] 重载模块: {module_name}")

    # 重新导入函数
    global advanced_calculator, batch_calculate, get_calculation_info, smart_calculator
    from server_func_1 import advanced_calculator, batch_calculate, get_calculation_info, smart_calculator

# 初始加载
reload_all_modules()

class HotUpdateServer:
    def __init__(self, host='localhost', port=8888):
        self.host = host
        self.port = port
        self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        self.last_reload_time = time.time()
        self.auto_reload = True  # 自动重载开关

        # 启动自动重载线程
        if self.auto_reload:
            self.start_auto_reload_thread()

    def start_auto_reload_thread(self):
        """
        启动自动重载线程 - 定期检查并重载模块
        """
        def auto_reload_worker():
            while self.auto_reload:
                try:
                    time.sleep(5)  # 每5秒检查一次
                    reload_all_modules()
                    self.last_reload_time = time.time()
                except Exception as e:
                    print(f"[自动重载错误] {e}")

        reload_thread = threading.Thread(target=auto_reload_worker, daemon=True)
        reload_thread.start()
        print("[热更新] 自动重载线程已启动")

    def force_reload(self):
        """
        强制重载所有模块
        """
        reload_all_modules()
        self.last_reload_time = time.time()
        return {"status": "success", "message": "模块重载完成", "time": self.last_reload_time}

    def get_server_status(self):
        """
        获取服务器状态信息
        """
        from server_func_1 import get_module_info

        return {
            "server_version": "2.0",
            "last_reload": self.last_reload_time,
            "auto_reload": self.auto_reload,
            "modules": get_module_info(),
            "uptime": time.time() - self.last_reload_time
        }

    def generate_executable_code(self, request_data):
        """
        新方案：不再生成代码字符串，而是直接调用最新的函数并返回结果
        客户端接收到结果后直接使用，实现真正的无感热更新
        """
        # 确保使用最新代码
        reload_all_modules()

        action = request_data.get('action', '')
        params = request_data.get('params', {})

        if action == 'calculate':
            num1 = params.get('num1', 0)
            num2 = params.get('num2', 0)
            operation = params.get('operation', 'add')

            # 直接调用最新的函数，返回结果
            try:
                result = advanced_calculator(num1, num2, operation)
                return {
                    "type": "result",
                    "data": result,
                    "message": "计算完成",
                    "server_version": "2.0_module_reload"
                }
            except Exception as e:
                return {
                    "type": "error",
                    "data": str(e),
                    "message": "计算失败"
                }

        elif action == 'batch_calculate':
            numbers = params.get('numbers', [])
            operation = params.get('operation', 'add')

            try:
                result = batch_calculate(numbers, operation)
                return {
                    "type": "result",
                    "data": result,
                    "message": "批量计算完成"
                }
            except Exception as e:
                return {
                    "type": "error",
                    "data": str(e),
                    "message": "批量计算失败"
                }

        elif action == 'smart_calculate':
            num1 = params.get('num1', 0)
            num2 = params.get('num2', None)
            operation = params.get('operation', 'auto')

            try:
                result = smart_calculator(num1, num2, operation)
                return {
                    "type": "result",
                    "data": result,
                    "message": "智能计算完成"
                }
            except Exception as e:
                return {
                    "type": "error",
                    "data": str(e),
                    "message": "智能计算失败"
                }

        elif action == 'info':
            try:
                info = get_calculation_info()
                return {
                    "type": "result",
                    "data": info,
                    "message": "信息获取完成"
                }
            except Exception as e:
                return {
                    "type": "error",
                    "data": str(e),
                    "message": "信息获取失败"
                }

        elif action == 'server_status':
            return {
                "type": "result",
                "data": self.get_server_status(),
                "message": "服务器状态获取完成"
            }

        elif action == 'force_reload':
            return {
                "type": "result",
                "data": self.force_reload(),
                "message": "强制重载完成"
            }

        return {
            "type": "error",
            "data": "未知的操作类型",
            "message": f"不支持的操作: {action}"
        }
    
    def handle_client(self, client_socket):
        """
        处理客户端请求 - 新方案：直接返回执行结果
        """
        try:
            # 接收请求数据
            data = client_socket.recv(1024).decode('utf-8')
            request = json.loads(data)

            print(f"[请求] {request}")

            # 直接执行并返回结果（不再返回代码字符串）
            result = self.generate_executable_code(request)

            # 发送结果给客户端
            response = {
                'status': 'success',
                'result': result,
                'timestamp': time.time(),
                'server_info': 'HotUpdateServer v2.0 - Module Reload'
            }

            client_socket.send(json.dumps(response, ensure_ascii=False).encode('utf-8'))
            print(f"[响应] {result.get('message', '处理完成')}")

        except Exception as e:
            error_response = {
                'status': 'error',
                'message': str(e),
                'timestamp': time.time()
            }
            client_socket.send(json.dumps(error_response, ensure_ascii=False).encode('utf-8'))
            print(f"[错误] {e}")

        finally:
            client_socket.close()
    
    def start(self):
        """
        启动服务器
        """
        self.socket.bind((self.host, self.port))
        self.socket.listen(5)
        print(f"🚀 模块热重载服务器启动在 {self.host}:{self.port}")
        print(f"📦 自动重载: {'开启' if self.auto_reload else '关闭'}")
        print(f"🔄 支持的操作: calculate, batch_calculate, smart_calculate, info, server_status, force_reload")
        print("=" * 60)

        try:
            while True:
                client_socket, address = self.socket.accept()
                print(f"🔗 客户端连接: {address}")
                self.handle_client(client_socket)
        except KeyboardInterrupt:
            print("\n🛑 服务器关闭")
            self.auto_reload = False  # 停止自动重载线程
        finally:
            self.socket.close()

if __name__ == "__main__":
    print("🌟 Python 模块热重载系统 v2.0")
    print("✨ 特性: 无感热更新、自动模块重载、智能计算")
    server = HotUpdateServer()
    server.start()
