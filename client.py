# client.py - 客户端代码文件
# 模块热重载客户端 - 真正的无感热更新

import socket
import json
import time

class HotUpdateClient:
    def __init__(self, host='localhost', port=8888):
        self.host = host
        self.port = port
    
    def send_request(self, action, params=None):
        """
        向服务端发送请求并获取可执行代码
        """
        if params is None:
            params = {}
        
        request = {
            'action': action,
            'params': params
        }
        
        try:
            # 创建socket连接
            client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            client_socket.connect((self.host, self.port))
            
            # 发送请求
            request_data = json.dumps(request, ensure_ascii=False)
            client_socket.send(request_data.encode('utf-8'))
            
            # 接收响应
            response_data = client_socket.recv(4096).decode('utf-8')
            response = json.loads(response_data)
            
            client_socket.close()
            
            return response
            
        except Exception as e:
            print(f"请求失败: {e}")
            return None
    
    def execute_hot_update(self, action, params=None):
        """
        新方案：无感热更新 - 直接获取服务端执行结果
        客户端不再需要exec()，服务端自动使用最新代码
        """
        print(f"\n🔄 执行热更新请求")
        print(f"📋 操作: {action}")
        print(f"📝 参数: {params}")

        # 从服务端获取执行结果（不是代码）
        response = self.send_request(action, params)

        if response and response.get('status') == 'success':
            result = response.get('result', {})
            server_info = response.get('server_info', '')
            timestamp = response.get('timestamp', time.time())

            print(f"\n✅ 服务端响应成功")
            print(f"🕐 时间戳: {timestamp:.2f}")
            print(f"🖥️  服务器: {server_info}")

            if result.get('type') == 'result':
                print(f"📊 结果: {result.get('data')}")
                print(f"💬 消息: {result.get('message')}")
                return result.get('data')
            elif result.get('type') == 'error':
                print(f"❌ 错误: {result.get('data')}")
                print(f"💬 消息: {result.get('message')}")
                return None
        else:
            print(f"❌ 请求失败: {response}")
            return None
    
    def demo_single_calculation(self):
        """
        演示单次计算 - 包含新功能（幂运算）
        """
        print("🧮【演示1】单次计算")
        self.execute_hot_update('calculate', {
            'num1': 10,
            'num2': 5,
            'operation': 'multiply'
        })

        print("\n🧮【演示1.1】新功能 - 幂运算")
        self.execute_hot_update('calculate', {
            'num1': 2,
            'num2': 8,
            'operation': 'power'
        })

    def demo_batch_calculation(self):
        """
        演示批量计算
        """
        print("\n📊【演示2】批量计算")
        self.execute_hot_update('batch_calculate', {
            'numbers': [1, 2, 3, 4, 5],
            'operation': 'add'
        })

    def demo_smart_calculation(self):
        """
        演示智能计算 - 新功能
        """
        print("\n🤖【演示3】智能计算（新功能）")
        self.execute_hot_update('smart_calculate', {
            'num1': 5,
            'operation': 'factorial'
        })

        print("\n🤖【演示3.1】智能自动选择")
        self.execute_hot_update('smart_calculate', {
            'num1': 15,
            'num2': 7,
            'operation': 'auto'
        })

    def demo_get_info(self):
        """
        演示获取服务器信息
        """
        print("\n📋【演示4】获取服务器信息")
        self.execute_hot_update('info')

    def demo_server_status(self):
        """
        演示获取服务器状态
        """
        print("\n🖥️【演示5】服务器状态")
        self.execute_hot_update('server_status')
    
    def interactive_mode(self):
        """
        交互模式 - 支持更多功能
        """
        print("\n" + "="*60)
        print("🎮 模块热重载客户端 - 交互模式")
        print("="*60)
        print("📋 可用命令:")
        print("1️⃣  calc - 单次计算")
        print("2️⃣  batch - 批量计算")
        print("3️⃣  smart - 智能计算（新功能）")
        print("4️⃣  info - 获取服务器信息")
        print("5️⃣  status - 服务器状态")
        print("6️⃣  reload - 强制重载服务端模块")
        print("7️⃣  demo - 运行所有演示")
        print("8️⃣  quit - 退出")
        print("="*60)

        while True:
            command = input("\n🎯 请输入命令: ").strip().lower()

            if command == 'quit':
                print("👋 退出客户端")
                break
            elif command == 'calc':
                try:
                    num1 = float(input("请输入第一个数字: "))
                    num2 = float(input("请输入第二个数字: "))
                    operation = input("请输入运算类型 (add/subtract/multiply/divide/power): ").strip()

                    self.execute_hot_update('calculate', {
                        'num1': num1,
                        'num2': num2,
                        'operation': operation
                    })
                except ValueError:
                    print("❌ 输入格式错误")
            elif command == 'batch':
                try:
                    numbers_str = input("请输入数字列表 (用逗号分隔): ")
                    numbers = [float(x.strip()) for x in numbers_str.split(',')]
                    operation = input("请输入运算类型 (add/subtract/multiply/divide): ").strip()

                    self.execute_hot_update('batch_calculate', {
                        'numbers': numbers,
                        'operation': operation
                    })
                except ValueError:
                    print("❌ 输入格式错误")
            elif command == 'smart':
                try:
                    num1 = float(input("请输入第一个数字: "))
                    num2_str = input("请输入第二个数字 (可选，按回车跳过): ").strip()
                    num2 = float(num2_str) if num2_str else None
                    operation = input("请输入运算类型 (auto/factorial/gcd): ").strip() or 'auto'

                    params = {'num1': num1, 'operation': operation}
                    if num2 is not None:
                        params['num2'] = num2

                    self.execute_hot_update('smart_calculate', params)
                except ValueError:
                    print("❌ 输入格式错误")
            elif command == 'info':
                self.execute_hot_update('info')
            elif command == 'status':
                self.execute_hot_update('server_status')
            elif command == 'reload':
                print("🔄 强制重载服务端模块...")
                self.execute_hot_update('force_reload')
            elif command == 'demo':
                self.demo_single_calculation()
                self.demo_batch_calculation()
                self.demo_smart_calculation()
                self.demo_get_info()
                self.demo_server_status()
            else:
                print("❌ 未知命令，请重新输入")

def main():
    """
    主函数
    """
    client = HotUpdateClient()

    print("🌟 Python 模块热重载系统 v2.0 - 客户端")
    print("✨ 特性: 无感热更新、实时模块重载、智能计算")
    print("🔗 准备连接服务端...")

    # 运行演示
    try:
        print("\n🎬 自动演示开始...")
        client.demo_single_calculation()
        client.demo_batch_calculation()
        client.demo_smart_calculation()
        client.demo_get_info()
        client.demo_server_status()

        print("\n🎉 演示完成！现在您可以:")
        print("1. 修改 server_func_2.py 或 server_func_1.py 中的函数")
        print("2. 服务端会自动重载（每5秒）")
        print("3. 客户端调用时自动使用最新代码")
        print("4. 无需重启任何程序！")

        # 进入交互模式
        client.interactive_mode()

    except KeyboardInterrupt:
        print("\n👋 客户端退出")
    except Exception as e:
        print(f"❌ 客户端运行错误: {e}")

if __name__ == "__main__":
    main()
