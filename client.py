# client.py - 客户端代码文件
# 实现热更新客户端

import socket
import json

class HotUpdateClient:
    def __init__(self, host='localhost', port=8888):
        self.host = host
        self.port = port
    
    def send_request(self, action, params=None):
        """
        向服务端发送请求并获取可执行代码
        """
        if params is None:
            params = {}
        
        request = {
            'action': action,
            'params': params
        }
        
        try:
            # 创建socket连接
            client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            client_socket.connect((self.host, self.port))
            
            # 发送请求
            request_data = json.dumps(request, ensure_ascii=False)
            client_socket.send(request_data.encode('utf-8'))
            
            # 接收响应
            response_data = client_socket.recv(4096).decode('utf-8')
            response = json.loads(response_data)
            
            client_socket.close()
            
            return response
            
        except Exception as e:
            print(f"请求失败: {e}")
            return None
    
    def execute_hot_update(self, action, params=None):
        """
        执行热更新：从服务端获取代码并执行
        """
        print(f"\n=== 执行热更新 ===")
        print(f"操作: {action}")
        print(f"参数: {params}")
        
        # 从服务端获取代码
        response = self.send_request(action, params)
        
        if response and response.get('status') == 'success':
            executable_code = response.get('code', '')
            
            print(f"\n--- 从服务端获取的代码 ---")
            print(executable_code)
            print("--- 代码结束 ---\n")
            
            # 执行从服务端获取的代码
            print("=== 执行服务端代码 ===")
            try:
                # 这里是热更新的核心：exec执行从服务端获取的代码
                exec(executable_code)
                print("=== 代码执行完成 ===\n")
            except Exception as e:
                print(f"代码执行失败: {e}")
        else:
            print(f"获取代码失败: {response}")
    
    def demo_single_calculation(self):
        """
        演示单次计算
        """
        print("【演示1】单次计算")
        self.execute_hot_update('calculate', {
            'num1': 10,
            'num2': 5,
            'operation': 'multiply'
        })
    
    def demo_batch_calculation(self):
        """
        演示批量计算
        """
        print("【演示2】批量计算")
        self.execute_hot_update('batch_calculate', {
            'numbers': [1, 2, 3, 4, 5],
            'operation': 'add'
        })
    
    def demo_get_info(self):
        """
        演示获取服务器信息
        """
        print("【演示3】获取服务器信息")
        self.execute_hot_update('info')
    
    def interactive_mode(self):
        """
        交互模式
        """
        print("=== 热更新客户端交互模式 ===")
        print("可用命令:")
        print("1. calc - 单次计算")
        print("2. batch - 批量计算") 
        print("3. info - 获取服务器信息")
        print("4. demo - 运行所有演示")
        print("5. quit - 退出")
        
        while True:
            command = input("\n请输入命令: ").strip().lower()
            
            if command == 'quit':
                print("退出客户端")
                break
            elif command == 'calc':
                try:
                    num1 = float(input("请输入第一个数字: "))
                    num2 = float(input("请输入第二个数字: "))
                    operation = input("请输入运算类型 (add/subtract/multiply/divide): ").strip()
                    
                    self.execute_hot_update('calculate', {
                        'num1': num1,
                        'num2': num2,
                        'operation': operation
                    })
                except ValueError:
                    print("输入格式错误")
            elif command == 'batch':
                try:
                    numbers_str = input("请输入数字列表 (用逗号分隔): ")
                    numbers = [float(x.strip()) for x in numbers_str.split(',')]
                    operation = input("请输入运算类型 (add/subtract/multiply/divide): ").strip()
                    
                    self.execute_hot_update('batch_calculate', {
                        'numbers': numbers,
                        'operation': operation
                    })
                except ValueError:
                    print("输入格式错误")
            elif command == 'info':
                self.execute_hot_update('info')
            elif command == 'demo':
                self.demo_single_calculation()
                self.demo_batch_calculation()
                self.demo_get_info()
            else:
                print("未知命令")

def main():
    """
    主函数
    """
    client = HotUpdateClient()
    
    print("=== Python 热更新系统演示 ===")
    print("客户端启动，准备连接服务端...")
    
    # 运行演示
    try:
        client.demo_single_calculation()
        client.demo_batch_calculation() 
        client.demo_get_info()
        
        # 进入交互模式
        client.interactive_mode()
        
    except KeyboardInterrupt:
        print("\n客户端退出")
    except Exception as e:
        print(f"客户端运行错误: {e}")

if __name__ == "__main__":
    main()
