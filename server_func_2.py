# server_func_2.py - 服务端底层函数文件
# 这是最底层的函数模块

def basic_math_operation(a, b, operation='add'):
    """
    基础数学运算函数
    支持加法、减法、乘法、除法
    """
    if operation == 'add':
        return a + b
    elif operation == 'subtract':
        return a - b
    elif operation == 'multiply':
        return a * b
    elif operation == 'divide':
        if b != 0:
            return a / b
        else:
            return "错误：除数不能为零"
    else:
        return "错误：不支持的运算类型"

def get_current_timestamp():
    """
    获取当前时间戳
    """
    import time
    return time.time()

def format_result(result, operation_type):
    """
    格式化计算结果
    """
    timestamp = get_current_timestamp()
    return f"[{timestamp}] {operation_type}运算结果: {result}"
