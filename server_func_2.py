# server_func_2.py - 服务端底层函数文件
# 版本: 1.0 - 支持动态重载
import time

# 模块版本标识 - 用于热更新检测
MODULE_VERSION = "1.0"
MODULE_NAME = "server_func_2"

def get_module_info():
    """
    获取模块信息 - 用于版本检测
    """
    return {
        "name": MODULE_NAME,
        "version": MODULE_VERSION,
        "functions": ["basic_math_operation", "get_current_timestamp", "format_result"],
        "last_modified": time.time()
    }

def basic_math_operation(a, b, operation='add'):
    """
    基础数学运算函数
    支持加法、减法、乘法、除法
    """
    print(f"[{MODULE_NAME} v{MODULE_VERSION}] 执行 {operation} 运算: {a} {operation} {b}")

    if operation == 'add':
        return a + b
    elif operation == 'subtract':
        return a - b
    elif operation == 'multiply':
        return a * b
    elif operation == 'divide':
        if b != 0:
            return a / b
        else:
            return "错误：除数不能为零"
    elif operation == 'power':  # 新增功能 - 演示热更新
        return a ** b
    else:
        return "错误：不支持的运算类型"

def get_current_timestamp():
    """
    获取当前时间戳
    """
    return time.time()

def format_result(result, operation_type):
    """
    格式化计算结果
    """
    timestamp = get_current_timestamp()
    return f"[{timestamp:.2f}] {operation_type}运算结果: {result} (来自 {MODULE_NAME} v{MODULE_VERSION})"

# 新增函数 - 演示热更新能力
def advanced_operation(a, b, operation='factorial'):
    """
    高级运算函数 - 这是热更新新增的功能
    """
    if operation == 'factorial':
        import math
        if isinstance(a, int) and a >= 0:
            return math.factorial(a)
        return "错误：阶乘需要非负整数"
    elif operation == 'gcd':
        import math
        return math.gcd(int(a), int(b))
    return "错误：不支持的高级运算"
